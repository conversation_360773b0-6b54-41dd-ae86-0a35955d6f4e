// @ts-nocheck
/**
 * @import {Syntax} from '../core.js'
 */
hpkp.displayName = 'hpkp'
hpkp.aliases = []

/** @type {Syntax} */
export default function hpkp(Prism) {
  /**
   * Original by <PERSON>.
   *
   * Reference: https://scotthelme.co.uk/hpkp-cheat-sheet/
   */

  Prism.languages.hpkp = {
    directive: {
      pattern:
        /\b(?:includeSubDomains|max-age|pin-sha256|preload|report-to|report-uri|strict)(?=[\s;=]|$)/i,
      alias: 'property'
    },
    operator: /=/,
    punctuation: /;/
  }
}
