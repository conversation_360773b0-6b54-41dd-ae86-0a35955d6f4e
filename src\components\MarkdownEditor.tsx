import React, { useState, useCallback } from 'react'
import { <PERSON><PERSON>, Eye, Edit3, FileText } from 'lucide-react'
import { debounce } from '../lib/utils'

interface MarkdownEditorProps {
  value: string
  onChange: (value: string) => void
  onCopy?: () => void
  className?: string
}

const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  value,
  onChange,
  onCopy,
  className = ''
}) => {
  const [mode, setMode] = useState<'edit' | 'preview' | 'split'>('edit')
  const [copySuccess, setCopySuccess] = useState(false)

  // 防抖处理输入变化
  const debouncedOnChange = useCallback(
    debounce((newValue: string) => {
      onChange(newValue)
    }, 300),
    [onChange]
  )

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    debouncedOnChange(newValue)
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(value)
      setCopySuccess(true)
      onCopy?.()
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  // 简单的Markdown渲染函数
  const renderMarkdown = (text: string): string => {
    return text
      // 标题
      .replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold text-text-primary mb-2 mt-4">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold text-text-primary mb-3 mt-4">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold text-text-primary mb-4 mt-4">$1</h1>')
      
      // 粗体和斜体
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-text-primary">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic text-text-secondary">$1</em>')
      
      // 代码块
      .replace(/```([\s\S]*?)```/g, '<pre class="bg-background-secondary p-3 rounded-lg my-3 overflow-x-auto"><code class="text-sm font-mono text-text-primary">$1</code></pre>')
      .replace(/`([^`]+)`/g, '<code class="bg-background-secondary px-1.5 py-0.5 rounded text-sm font-mono text-primary-600">$1</code>')
      
      // 链接
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-primary-600 hover:text-primary-700 underline" target="_blank" rel="noopener noreferrer">$1</a>')
      
      // 列表
      .replace(/^\* (.*$)/gim, '<li class="text-text-primary ml-4 mb-1">• $1</li>')
      .replace(/^- (.*$)/gim, '<li class="text-text-primary ml-4 mb-1">• $1</li>')
      .replace(/^\d+\. (.*$)/gim, '<li class="text-text-primary ml-4 mb-1 list-decimal">$1</li>')
      
      // 引用
      .replace(/^> (.*$)/gim, '<blockquote class="border-l-4 border-primary-200 pl-4 py-2 my-3 bg-primary-50 text-text-secondary italic">$1</blockquote>')
      
      // 分割线
      .replace(/^---$/gim, '<hr class="border-border-medium my-4">')
      
      // 段落
      .replace(/\n\n/g, '</p><p class="text-text-primary mb-3">')
      .replace(/^(?!<[h1-6]|<li|<blockquote|<pre|<hr)(.+)$/gim, '<p class="text-text-primary mb-3">$1</p>')
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* 工具栏 */}
      <div className="flex items-center justify-between p-3 border-b border-border-light bg-background-secondary">
        <div className="flex items-center gap-2">
          <FileText className="w-4 h-4 text-primary-600" />
          <span className="text-sm font-medium text-text-primary">Markdown 编辑器</span>
        </div>
        
        <div className="flex items-center gap-2">
          {/* 模式切换 */}
          <div className="flex items-center bg-background-tertiary rounded-lg p-1">
            <button
              onClick={() => setMode('edit')}
              className={`p-1.5 rounded text-xs font-medium transition-colors ${
                mode === 'edit'
                  ? 'bg-primary-600 text-white'
                  : 'text-text-secondary hover:text-primary-600'
              }`}
              title="编辑模式"
            >
              <Edit3 className="w-3 h-3" />
            </button>
            <button
              onClick={() => setMode('preview')}
              className={`p-1.5 rounded text-xs font-medium transition-colors ${
                mode === 'preview'
                  ? 'bg-primary-600 text-white'
                  : 'text-text-secondary hover:text-primary-600'
              }`}
              title="预览模式"
            >
              <Eye className="w-3 h-3" />
            </button>
          </div>
          
          {/* 复制按钮 */}
          <button
            onClick={handleCopy}
            className={`flex items-center gap-1.5 px-2.5 py-1.5 rounded-lg text-xs font-medium transition-colors ${
              copySuccess
                ? 'bg-green-600 text-white'
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
          >
            <Copy className="w-3 h-3" />
            {copySuccess ? '已复制!' : '复制'}
          </button>
        </div>
      </div>

      {/* 编辑器内容 */}
      <div className="flex-1 overflow-hidden">
        {mode === 'edit' && (
          <textarea
            value={value}
            onChange={handleInputChange}
            placeholder="在这里输入你的Markdown内容...

支持的语法：
# 标题 1
## 标题 2
### 标题 3

**粗体文本**
*斜体文本*

`行内代码`

```
代码块
```

- 无序列表
1. 有序列表

> 引用文本

[链接文本](https://example.com)

---"
            className="w-full h-full p-4 border-none outline-none resize-none bg-background-tertiary text-text-primary text-sm leading-relaxed"
            style={{ 
              fontFamily: 'Inter, system-ui, sans-serif',
              lineHeight: '1.6'
            }}
          />
        )}
        
        {mode === 'preview' && (
          <div className="h-full overflow-y-auto p-4 bg-background-tertiary">
            {value.trim() ? (
              <div 
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ 
                  __html: renderMarkdown(value) 
                }}
              />
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-center">
                <FileText className="w-12 h-12 text-text-muted mb-3" />
                <p className="text-text-secondary text-sm mb-1">暂无内容</p>
                <p className="text-text-muted text-xs">切换到编辑模式开始写作</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default MarkdownEditor
