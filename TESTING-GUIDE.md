# AI Helper Chrome扩展测试指南

## 🚀 安装扩展

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目中的 `dist/` 目录
6. 扩展安装成功后会出现在扩展列表中

## 🧪 功能测试

### 1. 基础功能测试

#### Popup界面测试
- [ ] 点击扩展图标，popup界面正常显示
- [ ] 界面采用Blue Topaz主题色彩（蓝色系）
- [ ] 标签切换功能正常（编辑器/Prompt管理）
- [ ] 编辑器标签默认激活

#### 编辑器功能测试
- [ ] 可以在编辑器中输入文本
- [ ] 支持Markdown语法
- [ ] 编辑器/预览模式切换正常
- [ ] 预览模式正确渲染Markdown
- [ ] 复制按钮功能正常
- [ ] 复制成功后显示"已复制!"提示

#### 数据持久化测试
- [ ] 输入内容后关闭popup，重新打开内容仍然存在
- [ ] 刷新页面后扩展数据不丢失

### 2. 侧边栏功能测试

#### 快捷键测试
- [ ] 按 `Alt+S` 可以切换侧边栏显示/隐藏
- [ ] 侧边栏从右侧滑入/滑出
- [ ] 侧边栏宽度为400px，不影响页面布局

#### 侧边栏界面测试
- [ ] 侧边栏头部显示"AI Helper"标题
- [ ] 功能切换按钮正常（编辑器/Prompt管理）
- [ ] 关闭按钮(×)可以隐藏侧边栏
- [ ] 编辑器功能与popup版本一致
- [ ] Prompt管理显示"正在开发中"占位符

#### 侧边栏编辑器测试
- [ ] 文本输入正常
- [ ] 复制按钮功能正常
- [ ] 内容自动保存（1秒延迟）
- [ ] 与popup编辑器数据同步

### 3. 兼容性测试

#### 页面兼容性
- [ ] 在不同网站上测试侧边栏功能
- [ ] 侧边栏不受页面CSS影响
- [ ] 侧边栏不影响页面正常功能
- [ ] 在有复杂布局的页面上测试

#### 浏览器兼容性
- [ ] Chrome最新版本
- [ ] Chrome较旧版本（如果可用）
- [ ] 不同操作系统（Windows/Mac/Linux）

### 4. 性能测试

#### 内存使用
- [ ] 扩展不会造成明显的内存泄漏
- [ ] 长时间使用后性能稳定

#### 响应速度
- [ ] 快捷键响应迅速（<100ms）
- [ ] 侧边栏动画流畅
- [ ] 编辑器输入无延迟

## 🐛 常见问题排查

### 扩展无法加载
1. 检查manifest.json语法是否正确
2. 确认所有必需文件都在dist目录中
3. 查看Chrome扩展页面的错误信息

### 快捷键不工作
1. 检查是否有其他扩展占用了Alt+S快捷键
2. 确认content script正确注入
3. 查看浏览器控制台是否有错误

### 侧边栏显示异常
1. 检查页面是否有CSS冲突
2. 确认content-script.css正确加载
3. 测试在简单页面（如about:blank）上是否正常

### 数据不保存
1. 检查Chrome存储权限
2. 确认background script正常运行
3. 查看扩展的存储使用情况

## 📝 测试报告模板

```
测试日期：____
测试环境：Chrome版本 ____ / 操作系统 ____
测试人员：____

基础功能：✅/❌
侧边栏功能：✅/❌
快捷键功能：✅/❌
数据持久化：✅/❌
兼容性：✅/❌

发现的问题：
1. ____
2. ____

建议改进：
1. ____
2. ____
```

## 🔧 调试技巧

1. **查看扩展日志**：在chrome://extensions/页面点击扩展的"详细信息"，然后查看"检查视图"
2. **调试popup**：右键点击扩展图标，选择"检查弹出内容"
3. **调试content script**：在页面上按F12，在Console中查看错误信息
4. **调试background script**：在扩展详情页面点击"background page"链接

## ✨ 下一步开发

测试完成后，可以考虑以下功能增强：
- [ ] 添加更多Markdown语法支持
- [ ] 实现Prompt管理功能
- [ ] 添加主题切换功能
- [ ] 支持导入/导出功能
- [ ] 添加快捷键自定义功能
