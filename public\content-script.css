/* Content Script CSS for AI Helper Chrome Extension */

/* 确保侧边栏不受页面样式影响 */
#ai-helper-sidebar * {
  box-sizing: border-box !important;
  font-family: 'Inter', system-ui, sans-serif !important;
}

/* 侧边栏滚动条样式 */
#ai-helper-sidebar ::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
}

#ai-helper-sidebar ::-webkit-scrollbar-track {
  background: #f4f6f8 !important;
  border-radius: 3px !important;
}

#ai-helper-sidebar ::-webkit-scrollbar-thumb {
  background: #d1d5db !important;
  border-radius: 3px !important;
}

#ai-helper-sidebar ::-webkit-scrollbar-thumb:hover {
  background: #9ca3af !important;
}

/* 按钮悬停效果 */
#ai-helper-sidebar button:hover {
  opacity: 0.9 !important;
  transform: translateY(-1px) !important;
  transition: all 0.2s ease !important;
}

/* 编辑器文本域样式 */
#ai-helper-editor {
  font-family: 'Inter', system-ui, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  color: #1f2937 !important;
  background: #ffffff !important;
}

#ai-helper-editor:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: -2px !important;
}

/* 标签按钮动画 */
#ai-helper-editor-tab,
#ai-helper-prompt-tab {
  transition: all 0.2s ease !important;
}

/* 复制按钮动画 */
#ai-helper-copy-btn {
  transition: all 0.2s ease !important;
}

/* 关闭按钮悬停效果 */
#ai-helper-close:hover {
  background: #f3f4f6 !important;
  color: #1f2937 !important;
}
