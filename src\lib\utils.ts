import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Chrome Extension 相关工具函数
export const chromeUtils = {
  // 检查是否在Chrome扩展环境中
  isExtensionContext: (): boolean => {
    return typeof (globalThis as any).chrome !== 'undefined' &&
           (globalThis as any).chrome.runtime &&
           (globalThis as any).chrome.runtime.id
  },

  // 发送消息到content script
  sendMessageToActiveTab: async (message: any): Promise<any> => {
    if (!chromeUtils.isExtensionContext()) {
      console.warn('Not in Chrome extension context')
      return
    }

    try {
      const chrome = (globalThis as any).chrome
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tab.id) {
        return await chrome.tabs.sendMessage(tab.id, message)
      }
    } catch (error) {
      console.error('Failed to send message to active tab:', error)
    }
  },

  // 保存数据到Chrome storage
  saveToStorage: async (data: Record<string, any>): Promise<void> => {
    if (!chromeUtils.isExtensionContext()) {
      console.warn('Not in Chrome extension context')
      return
    }

    try {
      const chrome = (globalThis as any).chrome
      await chrome.storage.local.set(data)
    } catch (error) {
      console.error('Failed to save to storage:', error)
    }
  },

  // 从Chrome storage读取数据
  loadFromStorage: async (keys: string[]): Promise<Record<string, any>> => {
    if (!chromeUtils.isExtensionContext()) {
      console.warn('Not in Chrome extension context')
      return {}
    }

    try {
      const chrome = (globalThis as any).chrome
      return await chrome.storage.local.get(keys)
    } catch (error) {
      console.error('Failed to load from storage:', error)
      return {}
    }
  },

  // 切换侧边栏
  toggleSidebar: async (): Promise<void> => {
    await chromeUtils.sendMessageToActiveTab({ action: 'toggle-sidebar' })
  },

  // 注入content script（如果需要）
  injectContentScript: async (): Promise<void> => {
    if (!chromeUtils.isExtensionContext()) return

    try {
      const chrome = (globalThis as any).chrome
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tab.id) {
        await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          files: ['content-script.js']
        })
      }
    } catch (error) {
      console.error('Failed to inject content script:', error)
    }
  }
}

// 格式化时间戳
export const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return date.toLocaleDateString('zh-CN')
}

// 复制文本到剪贴板
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    return false
  }
}

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout>
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}
