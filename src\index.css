@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  color-scheme: light;
  color: #1f2937;
  background-color: #fafbfc;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: #fafbfc;
  color: #1f2937;
}

/* Blue Topaz inspired custom styles */
.blue-topaz-editor {
  --editor-bg: #ffffff;
  --editor-border: #e5e7eb;
  --editor-text: #1f2937;
  --editor-text-muted: #6b7280;
  --editor-accent: #3b82f6;
  --editor-accent-light: #dbeafe;
  --editor-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f4f6f8;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Markdown editor custom styles */
.w-md-editor {
  background-color: var(--editor-bg) !important;
  border: 1px solid var(--editor-border) !important;
  border-radius: 0.5rem !important;
  box-shadow: var(--editor-shadow) !important;
}

.w-md-editor-text-textarea,
.w-md-editor-text {
  font-family: 'Inter', system-ui, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  color: var(--editor-text) !important;
}

.w-md-editor-preview {
  background-color: var(--editor-bg) !important;
  color: var(--editor-text) !important;
}

.w-md-editor-toolbar {
  background-color: var(--editor-bg) !important;
  border-bottom: 1px solid var(--editor-border) !important;
}

.w-md-editor-toolbar button {
  color: var(--editor-text-muted) !important;
}

.w-md-editor-toolbar button:hover {
  background-color: var(--editor-accent-light) !important;
  color: var(--editor-accent) !important;
}

/* Blue Topaz inspired animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* 增强的按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border: none;
  color: white;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.btn-secondary {
  background: white;
  border: 1px solid #d1d5db;
  color: #6b7280;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #f9fafb;
  border-color: #3b82f6;
  color: #3b82f6;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

/* 输入框样式 */
.input-field {
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  padding: 0.75rem;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.input-field:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 标签样式 */
.tab-button {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.tab-button.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.tab-button:not(.active) {
  background: white;
  color: #6b7280;
  border-color: #d1d5db;
}

.tab-button:not(.active):hover {
  background: #dbeafe;
  color: #3b82f6;
  border-color: #3b82f6;
}
