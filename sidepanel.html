<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手侧边栏</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/editor.css">
    <!-- 引入Quill编辑器 -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
</head>
<body>
    <div class="sidebar-container">
        <!-- 标题栏 -->
        <div class="header">
            <h1 class="title">AI助手</h1>
        </div>
        
        <!-- 标签页导航 -->
        <div class="tab-navigation">
            <button class="tab-btn active" data-tab="editor">
                <span class="tab-icon">📝</span>
                编辑器
            </button>
            <button class="tab-btn" data-tab="prompts">
                <span class="tab-icon">💡</span>
                Prompt管理
            </button>
        </div>
        
        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 编辑器标签页 -->
            <div class="tab-content active" id="editor-tab">
                <div class="editor-header">
                    <div class="editor-title">
                        <span class="editor-icon">📄</span>
                        <span>笔记编辑器</span>
                    </div>
                    <button class="copy-btn" id="copyBtn">
                        <span class="copy-icon">📋</span>
                        复制内容
                    </button>
                </div>
                
                <!-- Markdown编辑器容器 -->
                <div class="editor-container">
                    <div id="editor"></div>
                </div>
                
                <!-- 编辑器工具栏 -->
                <div class="editor-toolbar">
                    <button class="tool-btn" id="clearBtn">
                        <span>🗑️</span>
                        清空
                    </button>
                    <button class="tool-btn" id="saveBtn">
                        <span>💾</span>
                        保存
                    </button>
                </div>
            </div>
            
            <!-- Prompt管理标签页 -->
            <div class="tab-content" id="prompts-tab">
                <div class="prompts-placeholder">
                    <div class="placeholder-icon">💡</div>
                    <h3>Prompt管理</h3>
                    <p>此功能正在开发中...</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 引入Quill编辑器JS -->
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script src="scripts/editor.js"></script>
    <script src="scripts/main.js"></script>
</body>
</html>
