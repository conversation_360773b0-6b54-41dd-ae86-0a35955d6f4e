/* Chrome Extension Popup Styles */
#root {
  width: 384px;
  height: 600px;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f4f6f8;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 键盘快捷键样式 */
kbd {
  display: inline-block;
  padding: 2px 6px;
  font-size: 11px;
  line-height: 1;
  color: #6b7280;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  font-family: 'JetBrains Mono', Consolas, monospace;
}

/* 按钮动画效果 */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

/* 文本域焦点样式 */
textarea:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
}

/* Blue Topaz 主题特定样式 */
.blue-topaz-theme {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --accent-color: #0ea5e9;
  --background-primary: #fafbfc;
  --background-secondary: #f4f6f8;
  --background-tertiary: #ffffff;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
}
