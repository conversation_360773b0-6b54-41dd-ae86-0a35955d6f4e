# 图标生成说明

## 自动生成图标

1. 在浏览器中打开 `generate-icons.html` 文件
2. 页面会自动生成不同尺寸的PNG图标
3. 点击下载链接保存图标到 `public/icons/` 目录

## 手动创建图标

如果自动生成不工作，可以手动创建以下尺寸的图标：

- `icon-16.png` (16x16)
- `icon-32.png` (32x32) 
- `icon-48.png` (48x48)
- `icon-128.png` (128x128)

## 图标设计

图标采用Blue Topaz主题色彩：
- 主色：#3B82F6 (蓝色)
- 辅助色：#0EA5E9 (天蓝色)
- 背景：白色
- 设计元素：笔记本电脑 + 侧边栏，体现AI助手的功能

## 临时解决方案

在开发阶段，可以使用任何16x16、32x32、48x48、128x128的PNG图标文件作为占位符。
