@md-editor:~ "w-md-editor";

.@{md-editor} {
  &-toolbar-child {
    position: absolute;
    border-radius: 3px;
    box-shadow: 0 0 0 1px var(--md-editor-box-shadow-color), 0 0 0 var(--md-editor-box-shadow-color),
      0 1px 1px var(--md-editor-box-shadow-color);
    background-color: var(--md-editor-background-color);
    z-index: 1;
    display: none;
    &.active {
      display: block;
    }
    .@{md-editor}-toolbar {
      border-bottom: 0;
      padding: 3px;
      border-radius: 3px;
      ul > li {
        display: block;
        button {
          width: -webkit-fill-available;
          height: initial;
          box-sizing: border-box;
          padding: 3px 4px 2px 4px;
          margin: 0;
        }
      }
    }
  }
}
