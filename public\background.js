// Background script for AI Helper Chrome Extension

// 监听快捷键命令
chrome.commands.onCommand.addListener((command) => {
  if (command === 'toggle-sidebar') {
    // 向当前活动标签页发送切换侧边栏的消息
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, { 
          action: 'toggle-sidebar' 
        }).catch(() => {
          // 如果content script还没有注入，先注入再发送消息
          chrome.scripting.executeScript({
            target: { tabId: tabs[0].id },
            files: ['content-script.js']
          }).then(() => {
            chrome.tabs.sendMessage(tabs[0].id, { 
              action: 'toggle-sidebar' 
            });
          });
        });
      }
    });
  }
});

// 监听扩展安装事件
chrome.runtime.onInstalled.addListener(() => {
  console.log('AI Helper extension installed');
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'save-content') {
    // 保存编辑器内容到storage
    chrome.storage.local.set({
      editorContent: request.content,
      timestamp: Date.now()
    }).then(() => {
      sendResponse({ success: true });
    });
    return true; // 保持消息通道开放
  }
  
  if (request.action === 'load-content') {
    // 从storage加载编辑器内容
    chrome.storage.local.get(['editorContent']).then((result) => {
      sendResponse({ content: result.editorContent || '' });
    });
    return true; // 保持消息通道开放
  }
});
