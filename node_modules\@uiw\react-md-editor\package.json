{"name": "@uiw/react-md-editor", "version": "4.0.7", "description": "A markdown editor with preview, implemented with React.js and TypeScript.", "homepage": "https://uiwjs.github.io/react-md-editor/", "funding": "https://jaywcjlove.github.io/#/sponsor", "author": "kenny wang <<EMAIL>>", "main": "lib/index.js", "module": "esm/index.js", "exports": {"./README.md": "./README.md", "./package.json": "./package.json", "./markdown-editor.css": "./markdown-editor.css", ".": {"import": "./esm/index.js", "types": "./lib/index.d.ts", "require": "./lib/index.js"}, "./nohighlight": {"import": "./esm/index.nohighlight.js", "types": "./lib/index.nohighlight.d.ts", "require": "./lib/index.nohighlight.js"}, "./commands": {"import": "./esm/commands/index.js", "types": "./lib/commands/index.d.ts", "require": "./lib/commands/index.js"}, "./commands-cn": {"import": "./esm/commands/index.cn.js", "types": "./lib/commands/index.cn.d.ts", "require": "./lib/commands/index.cn.js"}}, "repository": {"type": "git", "url": "https://github.com/uiwjs/react-md-editor"}, "publishConfig": {"access": "public"}, "files": ["markdown-editor.css", "nohighlight.d.ts", "commands.d.ts", "commands-cn.d.ts", "lib", "dist", "esm", "src"], "license": "MIT", "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"@babel/runtime": "^7.14.6", "@uiw/react-markdown-preview": "^5.0.6", "rehype": "~13.0.0", "rehype-prism-plus": "~2.0.0"}, "keywords": ["react", "editor", "md-editor", "markdown", "react-markdown", "react-md-editor", "react-markdown-editor", "markdown-editor", "md", "uiw", "uiwjs", "code"]}