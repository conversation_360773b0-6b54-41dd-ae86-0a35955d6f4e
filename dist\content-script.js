// Content script for AI Helper Chrome Extension

let sidebarContainer = null;
let sidebarVisible = false;

// 创建侧边栏容器
function createSidebar() {
  if (sidebarContainer) return;

  // 创建侧边栏容器
  sidebarContainer = document.createElement('div');
  sidebarContainer.id = 'ai-helper-sidebar';
  sidebarContainer.style.cssText = `
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: #ffffff;
    border-left: 1px solid #e5e7eb;
    box-shadow: -4px 0 12px 0 rgba(0, 0, 0, 0.12);
    z-index: 2147483647;
    transition: right 0.3s ease-in-out;
    font-family: 'Inter', system-ui, sans-serif;
    overflow: hidden;
  `;

  // 创建侧边栏内容
  const sidebarContent = document.createElement('div');
  sidebarContent.innerHTML = `
    <div style="height: 100%; display: flex; flex-direction: column;">
      <!-- 头部 -->
      <div style="padding: 16px; border-bottom: 1px solid #e5e7eb; background: #fafbfc;">
        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 12px;">
          <h2 style="margin: 0; font-size: 18px; font-weight: 600; color: #1f2937;">AI Helper</h2>
          <button id="ai-helper-close" style="
            background: none; 
            border: none; 
            font-size: 20px; 
            cursor: pointer; 
            color: #6b7280;
            padding: 4px;
            border-radius: 4px;
            margin-left: auto;
          ">×</button>
        </div>
        <!-- 功能切换按钮 -->
        <div style="display: flex; gap: 8px;">
          <button id="ai-helper-editor-tab" style="
            padding: 8px 16px;
            border: 1px solid #3b82f6;
            background: #3b82f6;
            color: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
          ">编辑器</button>
          <button id="ai-helper-prompt-tab" style="
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: white;
            color: #6b7280;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
          ">Prompt管理</button>
        </div>
      </div>
      
      <!-- 内容区域 -->
      <div style="flex: 1; overflow: hidden;">
        <!-- 编辑器面板 -->
        <div id="ai-helper-editor-panel" style="height: 100%; display: flex; flex-direction: column;">
          <div style="padding: 16px; flex: 1; display: flex; flex-direction: column;">
            <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 12px;">
              <h3 style="margin: 0; font-size: 16px; font-weight: 500; color: #1f2937;">Markdown 编辑器</h3>
              <button id="ai-helper-copy-btn" style="
                padding: 6px 12px;
                background: #3b82f6;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                font-weight: 500;
              ">复制内容</button>
            </div>
            <div style="flex: 1; border: 1px solid #e5e7eb; border-radius: 6px; overflow: hidden;">
              <textarea id="ai-helper-editor" placeholder="在这里输入你的Markdown内容..." style="
                width: 100%;
                height: 100%;
                border: none;
                outline: none;
                padding: 16px;
                font-family: 'Inter', system-ui, sans-serif;
                font-size: 14px;
                line-height: 1.6;
                resize: none;
                background: #ffffff;
                color: #1f2937;
              "></textarea>
            </div>
          </div>
        </div>
        
        <!-- Prompt管理面板 -->
        <div id="ai-helper-prompt-panel" style="height: 100%; display: none; padding: 16px;">
          <h3 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 500; color: #1f2937;">Prompt 管理</h3>
          <div style="
            padding: 32px;
            text-align: center;
            color: #6b7280;
            background: #f9fafb;
            border-radius: 8px;
            border: 2px dashed #d1d5db;
          ">
            <p style="margin: 0; font-size: 14px;">此功能正在开发中...</p>
            <p style="margin: 8px 0 0 0; font-size: 12px;">敬请期待</p>
          </div>
        </div>
      </div>
    </div>
  `;

  sidebarContainer.appendChild(sidebarContent);
  document.body.appendChild(sidebarContainer);

  // 绑定事件
  bindSidebarEvents();
  
  // 加载保存的内容
  loadEditorContent();
}

// 绑定侧边栏事件
function bindSidebarEvents() {
  // 关闭按钮
  const closeBtn = document.getElementById('ai-helper-close');
  closeBtn.addEventListener('click', hideSidebar);

  // 标签切换
  const editorTab = document.getElementById('ai-helper-editor-tab');
  const promptTab = document.getElementById('ai-helper-prompt-tab');
  const editorPanel = document.getElementById('ai-helper-editor-panel');
  const promptPanel = document.getElementById('ai-helper-prompt-panel');

  editorTab.addEventListener('click', () => {
    editorTab.style.background = '#3b82f6';
    editorTab.style.color = 'white';
    editorTab.style.borderColor = '#3b82f6';
    
    promptTab.style.background = 'white';
    promptTab.style.color = '#6b7280';
    promptTab.style.borderColor = '#d1d5db';
    
    editorPanel.style.display = 'flex';
    promptPanel.style.display = 'none';
  });

  promptTab.addEventListener('click', () => {
    promptTab.style.background = '#3b82f6';
    promptTab.style.color = 'white';
    promptTab.style.borderColor = '#3b82f6';
    
    editorTab.style.background = 'white';
    editorTab.style.color = '#6b7280';
    editorTab.style.borderColor = '#d1d5db';
    
    editorPanel.style.display = 'none';
    promptPanel.style.display = 'block';
  });

  // 复制按钮
  const copyBtn = document.getElementById('ai-helper-copy-btn');
  copyBtn.addEventListener('click', copyEditorContent);

  // 编辑器内容变化时自动保存
  const editor = document.getElementById('ai-helper-editor');
  let saveTimeout;
  editor.addEventListener('input', () => {
    clearTimeout(saveTimeout);
    saveTimeout = setTimeout(() => {
      saveEditorContent();
    }, 1000); // 1秒后自动保存
  });
}

// 显示侧边栏
function showSidebar() {
  if (!sidebarContainer) createSidebar();
  sidebarContainer.style.right = '0px';
  sidebarVisible = true;
}

// 隐藏侧边栏
function hideSidebar() {
  if (sidebarContainer) {
    sidebarContainer.style.right = '-400px';
    sidebarVisible = false;
  }
}

// 切换侧边栏显示状态
function toggleSidebar() {
  if (sidebarVisible) {
    hideSidebar();
  } else {
    showSidebar();
  }
}

// 复制编辑器内容
function copyEditorContent() {
  const editor = document.getElementById('ai-helper-editor');
  if (editor) {
    navigator.clipboard.writeText(editor.value).then(() => {
      // 显示复制成功提示
      const copyBtn = document.getElementById('ai-helper-copy-btn');
      const originalText = copyBtn.textContent;
      copyBtn.textContent = '已复制!';
      copyBtn.style.background = '#10b981';
      setTimeout(() => {
        copyBtn.textContent = originalText;
        copyBtn.style.background = '#3b82f6';
      }, 1500);
    });
  }
}

// 保存编辑器内容
function saveEditorContent() {
  const editor = document.getElementById('ai-helper-editor');
  if (editor) {
    chrome.runtime.sendMessage({
      action: 'save-content',
      content: editor.value
    });
  }
}

// 加载编辑器内容
function loadEditorContent() {
  chrome.runtime.sendMessage({
    action: 'load-content'
  }, (response) => {
    if (response && response.content) {
      const editor = document.getElementById('ai-helper-editor');
      if (editor) {
        editor.value = response.content;
      }
    }
  });
}

// 监听来自background script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'toggle-sidebar') {
    toggleSidebar();
    sendResponse({ success: true });
  }
});

// 监听键盘事件
document.addEventListener('keydown', (event) => {
  if (event.altKey && event.key === 's') {
    event.preventDefault();
    toggleSidebar();
  }
});

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', createSidebar);
} else {
  createSidebar();
}
