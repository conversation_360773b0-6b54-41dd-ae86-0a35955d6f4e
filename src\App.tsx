import { useState, useEffect } from 'react'
import { Settings, FileText, MessageSquare, X } from 'lucide-react'
import MarkdownEditor from './components/MarkdownEditor'
import { chromeUtils, debounce } from './lib/utils'
import './App.css'

function App() {
  const [activeTab, setActiveTab] = useState<'editor' | 'prompt'>('editor')
  const [editorContent, setEditorContent] = useState('')

  // 加载保存的内容
  useEffect(() => {
    const loadContent = async () => {
      const result = await chromeUtils.loadFromStorage(['editorContent'])
      if (result.editorContent) {
        setEditorContent(result.editorContent)
      }
    }
    loadContent()
  }, [])

  // 防抖保存内容
  const debouncedSave = debounce(async (content: string) => {
    await chromeUtils.saveToStorage({
      editorContent: content,
      timestamp: Date.now()
    })
  }, 1000)

  // 处理编辑器内容变化
  const handleEditorChange = (value: string) => {
    setEditorContent(value)
    debouncedSave(value)
  }

  // 切换侧边栏
  const toggleSidebar = async () => {
    await chromeUtils.toggleSidebar()
  }

  return (
    <div className="w-96 h-[600px] bg-background-primary flex flex-col">
      {/* 头部 */}
      <div className="p-4 border-b border-border-light bg-background-secondary">
        <div className="flex items-center justify-between mb-3">
          <h1 className="text-lg font-semibold text-text-primary flex items-center gap-2">
            <Settings className="w-5 h-5 text-primary-600" />
            AI Helper
          </h1>
          <button
            onClick={toggleSidebar}
            className="p-2 rounded-lg hover:bg-background-primary transition-colors"
            title="切换侧边栏 (Alt+S)"
          >
            <X className="w-4 h-4 text-text-secondary" />
          </button>
        </div>

        {/* 标签切换 */}
        <div className="flex gap-2">
          <button
            onClick={() => setActiveTab('editor')}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeTab === 'editor'
                ? 'bg-primary-600 text-white'
                : 'bg-background-tertiary text-text-secondary hover:bg-primary-50 hover:text-primary-600'
            }`}
          >
            <FileText className="w-4 h-4" />
            编辑器
          </button>
          <button
            onClick={() => setActiveTab('prompt')}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeTab === 'prompt'
                ? 'bg-primary-600 text-white'
                : 'bg-background-tertiary text-text-secondary hover:bg-primary-50 hover:text-primary-600'
            }`}
          >
            <MessageSquare className="w-4 h-4" />
            Prompt管理
          </button>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'editor' ? (
          <MarkdownEditor
            value={editorContent}
            onChange={handleEditorChange}
            className="h-full"
          />
        ) : (
          <div className="h-full p-4">
            <h2 className="text-base font-medium text-text-primary mb-4">Prompt 管理</h2>
            <div className="flex flex-col items-center justify-center h-64 bg-background-secondary rounded-lg border-2 border-dashed border-border-medium">
              <MessageSquare className="w-12 h-12 text-text-muted mb-3" />
              <p className="text-text-secondary text-sm mb-1">此功能正在开发中...</p>
              <p className="text-text-muted text-xs">敬请期待</p>
            </div>
          </div>
        )}
      </div>

      {/* 底部提示 */}
      <div className="p-3 border-t border-border-light bg-background-secondary">
        <p className="text-xs text-text-muted text-center">
          按 <kbd className="px-1.5 py-0.5 bg-background-tertiary rounded text-text-secondary">Alt+S</kbd> 切换侧边栏
        </p>
      </div>
    </div>
  )
}

export default App
