@md-editor:~ "w-md-editor";

.@{md-editor} {
  text-align: left;
  border-radius: 3px;
  padding-bottom: 1px;
  position: relative;
  color: var(--color-fg-default);
  --md-editor-font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  --md-editor-background-color: var(--color-canvas-default, #ffffff);
  --md-editor-box-shadow-color: var(--color-border-default, #d0d7de);
  box-shadow: 0 0 0 1px var(--md-editor-box-shadow-color), 0 0 0 var(--md-editor-box-shadow-color),
    0 1px 1px var(--md-editor-box-shadow-color);
  background-color: var(--md-editor-background-color);
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  display: flex;
  flex-direction: column;
  &&-rtl {
    direction: rtl !important;
    text-align: right !important;
  }
  &&-rtl &-preview {
    right: unset !important;
    left: 0;
    text-align: right !important;
    box-shadow: inset -1px 0 0 0 var(--md-editor-box-shadow-color);
  }
  &&-rtl &-text {
    text-align: right !important;
  }
  &-toolbar {
    height: fit-content;
  }
  &-content {
    height: 100%;
    overflow: auto;
    position: relative;
    border-radius: 0 0 3px 0;
  }
  .copied {
    display: none !important;
  }
  &-input {
    width: 50%;
    height: 100%;
  }
  &-text-pre > code {
    word-break: break-word !important;
    white-space: pre-wrap !important;
  }
  &-preview {
    width: 50%;
    box-sizing: border-box;
    box-shadow: inset 1px 0 0 0 var(--md-editor-box-shadow-color);
    position: absolute;
    padding: 10px 20px;
    overflow: auto;
    top: 0;
    right: 0;
    bottom: 0;
    border-radius: 0 0 5px 0;
    display: flex;
    flex-direction: column;
    // word-break: break-word !important;
    // white-space: pre-wrap !important;
    .anchor {
      display: none;
    }
    .contains-task-list li.task-list-item {
      list-style: none;
    }
  }
  &-show-preview &-input {
    width: 0%;
    overflow: hidden;
    background-color: var(--md-editor-background-color);
  }
  &-show-preview &-preview {
    width: 100%;
    box-shadow: inset 0 0 0 0;
  }
  &-show-edit &-input {
    width: 100%;
  }
  &-show-edit &-preview {
    width: 0%;
    padding: 0;
  }
  &-fullscreen {
    overflow: hidden;
    position: fixed;
    z-index: 99999;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100% !important;
  }
  &-fullscreen &-content {
    height: 100%;
  }
}
