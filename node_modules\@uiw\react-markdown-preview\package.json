{"name": "@uiw/react-markdown-preview", "version": "5.1.4", "description": "React component preview markdown text in web browser. The minimal amount of CSS to replicate the GitHub Markdown style.", "homepage": "https://uiwjs.github.io/react-markdown-preview", "funding": "https://jaywcjlove.github.io/#/sponsor", "main": "lib/index.js", "module": "esm/index.js", "exports": {"./README.md": "./README.md", "./package.json": "./package.json", "./markdown.css": "./markdown.css", ".": {"import": "./esm/index.js", "types": "./lib/index.d.ts", "require": "./lib/index.js"}, "./nohighlight": {"import": "./esm/nohighlight.js", "types": "./lib/nohighlight.d.ts", "require": "./lib/nohighlight.js"}}, "repository": {"type": "git", "url": "https://github.com/uiwjs/react-markdown-preview.git"}, "author": "kenny wang <<EMAIL>>", "license": "MIT", "files": ["dist", "lib", "esm", "nohighlight.d.ts", "markdown.css", "src/**/*.{ts,tsx,less}"], "keywords": ["react", "markdown", "prismjs", "react-markdown"], "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"@babel/runtime": "^7.17.2", "@uiw/copy-to-clipboard": "~1.0.12", "react-markdown": "~9.0.1", "rehype-attr": "~3.0.1", "rehype-autolink-headings": "~7.1.0", "rehype-ignore": "^2.0.0", "rehype-prism-plus": "2.0.0", "rehype-raw": "^7.0.0", "rehype-rewrite": "~4.0.0", "rehype-slug": "~6.0.0", "remark-gfm": "~4.0.0", "remark-github-blockquote-alert": "^1.0.0", "unist-util-visit": "^5.0.0"}}