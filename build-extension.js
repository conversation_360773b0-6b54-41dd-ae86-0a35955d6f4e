const fs = require('fs');
const path = require('path');

// 构建Chrome扩展的脚本
async function buildExtension() {
  console.log('🚀 开始构建Chrome扩展...');
  
  try {
    // 1. 确保dist目录存在
    const distDir = path.join(__dirname, 'dist');
    if (!fs.existsSync(distDir)) {
      fs.mkdirSync(distDir, { recursive: true });
    }
    
    // 2. 复制manifest.json
    const manifestSrc = path.join(__dirname, 'public', 'manifest.json');
    const manifestDest = path.join(distDir, 'manifest.json');
    if (fs.existsSync(manifestSrc)) {
      fs.copyFileSync(manifestSrc, manifestDest);
      console.log('✅ 复制 manifest.json');
    }
    
    // 3. 复制background.js
    const backgroundSrc = path.join(__dirname, 'public', 'background.js');
    const backgroundDest = path.join(distDir, 'background.js');
    if (fs.existsSync(backgroundSrc)) {
      fs.copyFileSync(backgroundSrc, backgroundDest);
      console.log('✅ 复制 background.js');
    }
    
    // 4. 复制content-script.js
    const contentScriptSrc = path.join(__dirname, 'public', 'content-script.js');
    const contentScriptDest = path.join(distDir, 'content-script.js');
    if (fs.existsSync(contentScriptSrc)) {
      fs.copyFileSync(contentScriptSrc, contentScriptDest);
      console.log('✅ 复制 content-script.js');
    }
    
    // 5. 复制content-script.css
    const contentCssSrc = path.join(__dirname, 'public', 'content-script.css');
    const contentCssDest = path.join(distDir, 'content-script.css');
    if (fs.existsSync(contentCssSrc)) {
      fs.copyFileSync(contentCssSrc, contentCssDest);
      console.log('✅ 复制 content-script.css');
    }
    
    // 6. 复制图标目录
    const iconsSrcDir = path.join(__dirname, 'public', 'icons');
    const iconsDestDir = path.join(distDir, 'icons');
    if (fs.existsSync(iconsSrcDir)) {
      if (!fs.existsSync(iconsDestDir)) {
        fs.mkdirSync(iconsDestDir, { recursive: true });
      }
      
      const iconFiles = fs.readdirSync(iconsSrcDir);
      iconFiles.forEach(file => {
        const srcFile = path.join(iconsSrcDir, file);
        const destFile = path.join(iconsDestDir, file);
        fs.copyFileSync(srcFile, destFile);
      });
      console.log('✅ 复制图标文件');
    }
    
    // 7. 创建简单的占位符图标（如果不存在）
    const iconSizes = [16, 32, 48, 128];
    iconSizes.forEach(size => {
      const iconPath = path.join(iconsDestDir, `icon-${size}.png`);
      if (!fs.existsSync(iconPath)) {
        // 创建一个简单的占位符文件
        fs.writeFileSync(iconPath, '');
        console.log(`⚠️  创建占位符图标: icon-${size}.png`);
      }
    });
    
    console.log('🎉 Chrome扩展构建完成！');
    console.log('📁 构建文件位于: dist/');
    console.log('');
    console.log('📋 下一步操作:');
    console.log('1. 运行 npm run build 构建React应用');
    console.log('2. 在Chrome中加载 dist/ 目录作为未打包的扩展');
    console.log('3. 如需图标，请打开 generate-icons.html 生成PNG图标');
    
  } catch (error) {
    console.error('❌ 构建失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  buildExtension();
}

module.exports = buildExtension;
